// 基金数据API路由

import { NextRequest, NextResponse } from 'next/server';
import { fundApiService } from '@/lib/api/fundApi';
import type { FundData } from '@/types/fund';

// GET /api/fund?code=000628&startDate=2024-01-01&endDate=2024-12-31
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const fundCode = searchParams.get('code');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    if (!fundCode) {
      return NextResponse.json(
        { error: '基金代码不能为空' },
        { status: 400 }
      );
    }

    // 验证基金代码格式
    if (!/^\d{6}$/.test(fundCode)) {
      return NextResponse.json(
        { error: '基金代码格式错误，应为6位数字' },
        { status: 400 }
      );
    }

    // 获取基金数据
    const fundData: FundData[] = await fundApiService.getFundData(
      fundCode,
      startDate || undefined,
      endDate || undefined,
      {
        useCache: true,
        timeout: 15000,
        retryCount: 2
      }
    );

    return NextResponse.json({
      success: true,
      data: fundData,
      meta: {
        fundCode,
        startDate,
        endDate,
        count: fundData.length
      }
    });

  } catch (error) {
    console.error('获取基金数据失败:', error);
    
    const errorMessage = error instanceof Error ? error.message : '未知错误';
    
    return NextResponse.json(
      { 
        success: false,
        error: errorMessage,
        code: 'FUND_DATA_ERROR'
      },
      { status: 500 }
    );
  }
}

// POST /api/fund/validate
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { fundCode } = body;

    if (!fundCode) {
      return NextResponse.json(
        { error: '基金代码不能为空' },
        { status: 400 }
      );
    }

    const isValid = await fundApiService.validateFund(fundCode);
    const fundInfo = isValid ? await fundApiService.getFundInfo(fundCode) : null;

    return NextResponse.json({
      success: true,
      data: {
        isValid,
        fundInfo
      }
    });

  } catch (error) {
    console.error('验证基金代码失败:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : '验证失败',
        code: 'VALIDATION_ERROR'
      },
      { status: 500 }
    );
  }
}
