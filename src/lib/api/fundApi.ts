// 基金数据API服务

import type {
  BaiduApiResponse,
  ParsedFundDataPoint,
  DataFetchOptions,
  PerformanceComparisonData,
  FundApiParams
} from './types';
import type { FundData } from '@/types/fund';
import {
  parseFundData,
  parsePerformanceData,
  buildApiUrl,
  validateFundCode as validateFundCodeFormat,
  validateApiParams,
  createApiError,
  CacheManager,
  withRetry,
  withTimeout,
  generateCacheKey,
  validateDateRange,
  formatErrorMessage
} from './utils';

/**
 * 基金API服务类
 */
export class FundApiService {
  private cacheManager: CacheManager;
  private defaultOptions: Required<DataFetchOptions> = {
    useCache: true,
    timeout: 10000,
    retryCount: 3,
    retryDelay: 1000
  };

  constructor() {
    this.cacheManager = CacheManager.getInstance();
  }

  /**
   * 获取基金历史净值数据
   */
  async getFundData(
    fundCode: string, 
    startDate?: string, 
    endDate?: string,
    options: DataFetchOptions = {}
  ): Promise<FundData[]> {
    const opts = { ...this.defaultOptions, ...options };

    // 验证基金代码
    if (!validateFundCodeFormat(fundCode)) {
      throw createApiError('INVALID_FUND_CODE', `无效的基金代码: ${fundCode}`);
    }

    // 验证日期范围
    if (startDate && endDate && !validateDateRange(startDate, endDate)) {
      throw createApiError('INVALID_DATE_RANGE', '开始日期不能晚于结束日期');
    }

    // 检查缓存
    const cacheKey = generateCacheKey(fundCode, startDate, endDate);
    if (opts.useCache) {
      const cachedData = this.cacheManager.get<FundData[]>(cacheKey);
      if (cachedData) {
        return cachedData;
      }
    }

    try {
      // 获取原始数据
      const rawData = await this.fetchRawData(fundCode, opts);
      
      // 解析数据
      const parsedData = parseFundData(rawData);
      
      // 转换为项目内部格式
      let fundData = this.convertToFundData(parsedData);
      
      // 应用日期过滤
      if (startDate || endDate) {
        fundData = this.filterByDateRange(fundData, startDate, endDate);
      }

      // 缓存结果
      if (opts.useCache) {
        this.cacheManager.set(cacheKey, fundData);
      }

      return fundData;
    } catch (error) {
      const errorMessage = formatErrorMessage(error);
      throw createApiError('API_ERROR', `获取基金数据失败: ${errorMessage}`, error);
    }
  }

  /**
   * 获取基金基本信息
   */
  async getFundInfo(fundCode: string): Promise<{ code: string; name: string } | null> {
    if (!validateFundCodeFormat(fundCode)) {
      return null;
    }

    try {
      const rawData = await this.fetchRawData(fundCode);
      
      // 从API响应中提取基金名称
      const result = rawData.Result?.[0];
      const query = result?.DisplayData?.resultData?.extData?.OriginQuery;
      
      if (query === fundCode) {
        // 这里可以扩展获取更多基金信息的逻辑
        return {
          code: fundCode,
          name: `基金${fundCode}` // 临时名称，实际应从其他API获取
        };
      }

      return null;
    } catch (error) {
      console.warn(`获取基金${fundCode}信息失败:`, error);
      return null;
    }
  }

  /**
   * 验证基金代码是否存在
   */
  async validateFund(fundCode: string): Promise<boolean> {
    try {
      const info = await this.getFundInfo(fundCode);
      return info !== null;
    } catch {
      return false;
    }
  }

  /**
   * 获取基金业绩对比数据
   */
  async getPerformanceComparison(
    fundCode: string,
    months: number = 12,
    options?: DataFetchOptions
  ): Promise<PerformanceComparisonData> {
    // 验证参数
    const validation = validateApiParams({
      fundCode,
      dataType: 'ai',
      months,
      source: 'qieman'
    });

    if (!validation.valid) {
      throw createApiError('INVALID_PARAMS', validation.errors.join(', '));
    }

    const opts = { ...this.defaultOptions, ...options };
    const cacheKey = `performance_${fundCode}_${months}`;

    // 检查缓存
    if (opts.useCache) {
      const cached = this.cacheManager.get<PerformanceComparisonData>(cacheKey);
      if (cached) {
        return cached;
      }
    }

    try {
      // 获取原始数据
      const rawData = await this.fetchRawPerformanceData(fundCode, months, opts);

      // 解析数据
      const performanceData = parsePerformanceData(rawData);

      // 缓存结果
      if (opts.useCache) {
        this.cacheManager.set(cacheKey, performanceData);
      }

      return performanceData;
    } catch (error) {
      const errorMessage = formatErrorMessage(error);
      throw createApiError('API_ERROR', `获取业绩对比数据失败: ${errorMessage}`, error);
    }
  }

  /**
   * 获取原始API数据
   */
  private async fetchRawData(
    fundCode: string,
    options: Required<DataFetchOptions> = this.defaultOptions
  ): Promise<BaiduApiResponse> {
    // 在浏览器环境中，优先使用Next.js API路由避免CORS问题
    if (typeof window !== 'undefined') {
      return this.fetchViaApiRoute(fundCode, options);
    }

    // 在服务端环境中，直接调用外部API
    const url = buildApiUrl(fundCode);

    const fetchWithRetry = () => withRetry(
      async () => {
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.ResultCode !== '0') {
          throw new Error(`API错误: ${data.ResultCode}`);
        }

        return data as BaiduApiResponse;
      },
      { maxRetries: options.retryCount, delay: options.retryDelay }
    );

    return withTimeout(fetchWithRetry(), options.timeout);
  }

  /**
   * 通过Next.js API路由获取数据（避免CORS问题）
   */
  private async fetchViaApiRoute(
    fundCode: string,
    options: Required<DataFetchOptions>
  ): Promise<BaiduApiResponse> {
    const url = buildApiUrl(fundCode);

    const fetchWithRetry = () => withRetry(
      async () => {
        // 直接调用百度API，在客户端可能遇到CORS问题
        // 这里保持原有逻辑，如果遇到CORS问题，可以考虑使用代理
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Accept': 'application/json, text/plain, */*',
            'Cache-Control': 'no-cache',
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.ResultCode !== '0') {
          throw new Error(`API错误: ${data.ResultCode}`);
        }

        return data as BaiduApiResponse;
      },
      { maxRetries: options.retryCount, delay: options.retryDelay }
    );

    return withTimeout(fetchWithRetry(), options.timeout);
  }

  /**
   * 获取业绩对比原始数据
   */
  private async fetchRawPerformanceData(
    fundCode: string,
    months: number,
    options: Required<DataFetchOptions>
  ): Promise<BaiduApiResponse> {
    // 在服务端环境中，直接调用外部API
    const url = buildApiUrl(fundCode, {
      dataType: 'ai',
      months,
      source: 'qieman'
    });

    const fetchWithRetry = () => withRetry(
      async () => {
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.ResultCode !== '0') {
          throw new Error(`API错误: ${data.ResultCode}`);
        }

        return data as BaiduApiResponse;
      },
      { maxRetries: options.retryCount, delay: options.retryDelay }
    );

    return withTimeout(fetchWithRetry(), options.timeout);
  }

  /**
   * 转换为项目内部数据格式
   */
  private convertToFundData(parsedData: ParsedFundDataPoint[]): FundData[] {
    return parsedData.map(point => ({
      date: point.date,
      netAssetValue: point.netAssetValue,
      accumulatedValue: point.accumulatedValue,
      volume: undefined // 百度API不提供成交量数据
    }));
  }

  /**
   * 按日期范围过滤数据
   */
  private filterByDateRange(
    data: FundData[], 
    startDate?: string, 
    endDate?: string
  ): FundData[] {
    return data.filter(item => {
      const itemDate = new Date(item.date);
      
      if (startDate && itemDate < new Date(startDate)) {
        return false;
      }
      
      if (endDate && itemDate > new Date(endDate)) {
        return false;
      }
      
      return true;
    });
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cacheManager.clear();
  }

  /**
   * 获取缓存统计
   */
  getCacheStats(): { size: number } {
    return {
      size: this.cacheManager.size()
    };
  }
}

// 导出单例实例
export const fundApiService = new FundApiService();

// 便捷函数
export async function fetchFundData(
  fundCode: string,
  startDate?: string,
  endDate?: string,
  options?: DataFetchOptions
): Promise<FundData[]> {
  return fundApiService.getFundData(fundCode, startDate, endDate, options);
}

export async function validateFund(fundCode: string): Promise<boolean> {
  return fundApiService.validateFund(fundCode);
}

export async function getFundBasicInfo(fundCode: string) {
  return fundApiService.getFundInfo(fundCode);
}

export async function getPerformanceComparison(
  fundCode: string,
  months?: number,
  options?: DataFetchOptions
): Promise<PerformanceComparisonData> {
  return fundApiService.getPerformanceComparison(fundCode, months, options);
}
