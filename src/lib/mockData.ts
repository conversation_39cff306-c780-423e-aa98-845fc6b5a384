import type { Fund, Index, FundData, IndexData } from "@/types/fund";
import { APP_CONFIG } from "@/lib/config";
import { fetchFundData, getFundBasicInfo } from "@/lib/api/fundApi";

// 真实基金数据
export const mockFunds: Fund[] = [
  {
    id: "fund_022919",
    name: "申万菱信中证500指数优选增强Y",
    code: "022919",
    type: "index",
    indexId: "index_zz500",
    description: "跟踪中证500指数的增强型指数基金",
  },
  {
    id: "fund_022926",
    name: "华宝中证A100ETF联接Y",
    code: "022926",
    type: "index",
    indexId: "index_a100",
    description: "跟踪中证A100指数的ETF联接基金",
  },
  {
    id: "fund_022949",
    name: "汇添富沪深300指数增强Y",
    code: "022949",
    type: "index",
    indexId: "index_hs300",
    description: "跟踪沪深300指数的增强型指数基金",
  },
  {
    id: "fund_022893",
    name: "汇添富中证500指数增强Y",
    code: "022893",
    type: "index",
    indexId: "index_zz500",
    description: "跟踪中证500指数的增强型指数基金",
  },
  {
    id: "fund_022971",
    name: "广发中证A500ETF联接Y",
    code: "022971",
    type: "index",
    indexId: "index_a500",
    description: "跟踪中证A500指数的ETF联接基金",
  },
  {
    id: "fund_022964",
    name: "广发沪深300ETF联接Y",
    code: "022964",
    type: "index",
    indexId: "index_hs300",
    description: "跟踪沪深300指数的ETF联接基金",
  },
  {
    id: "fund_022965",
    name: "广发中证500ETF联接Y",
    code: "022965",
    type: "index",
    indexId: "index_zz500",
    description: "跟踪中证500指数的ETF联接基金",
  },
  {
    id: "fund_022896",
    name: "广发创业板ETF发起式联接Y",
    code: "022896",
    type: "index",
    indexId: "index_cyb",
    description: "跟踪创业板指数的ETF联接基金",
  },
  {
    id: "fund_022962",
    name: "兴全沪深300指数增强（LOF）Y",
    code: "022962",
    type: "index",
    indexId: "index_hs300",
    description: "跟踪沪深300指数的增强型指数基金",
  },
  {
    id: "fund_022963",
    name: "兴全中证800六个月持有指数增强Y",
    code: "022963",
    type: "index",
    indexId: "index_zz800",
    description: "跟踪中证800指数的增强型指数基金",
  },
  {
    id: "fund_022967",
    name: "万家中证A500ETF发起式联接Y",
    code: "022967",
    type: "index",
    indexId: "index_a500",
    description: "跟踪中证A500指数的ETF联接基金",
  },
  {
    id: "fund_022917",
    name: "万家沪深300指数增强Y",
    code: "022917",
    type: "index",
    indexId: "index_hs300",
    description: "跟踪沪深300指数的增强型指数基金",
  },
  {
    id: "fund_022979",
    name: "华夏中证A500ETF联接Y",
    code: "022979",
    type: "index",
    indexId: "index_a500",
    description: "跟踪中证A500指数的ETF联接基金",
  },
  {
    id: "fund_022945",
    name: "华夏上证科创板50成份ETF联接Y",
    code: "022945",
    type: "index",
    indexId: "index_kc50",
    description: "跟踪科创50指数的ETF联接基金",
  },
  {
    id: "fund_022939",
    name: "华夏科创创业50ETF发起式联接Y",
    code: "022939",
    type: "index",
    indexId: "index_kccyb50",
    description: "跟踪科创创业50指数的ETF联接基金",
  },
  {
    id: "fund_022958",
    name: "华夏中证500ETF联接Y",
    code: "022958",
    type: "index",
    indexId: "index_zz500",
    description: "跟踪中证500指数的ETF联接基金",
  },
  {
    id: "fund_022954",
    name: "华夏沪深300指数增强Y",
    code: "022954",
    type: "index",
    indexId: "index_hs300",
    description: "跟踪沪深300指数的增强型指数基金",
  },
  {
    id: "fund_022957",
    name: "华夏中证500指数增强Y",
    code: "022957",
    type: "index",
    indexId: "index_zz500",
    description: "跟踪中证500指数的增强型指数基金",
  },
  {
    id: "fund_022948",
    name: "华泰柏瑞沪深300ETF联接Y",
    code: "022948",
    type: "index",
    indexId: "index_hs300",
    description: "跟踪沪深300指数的ETF联接基金",
  },
  {
    id: "fund_022947",
    name: "华泰柏瑞中证A500ETF联接Y",
    code: "022947",
    type: "index",
    indexId: "index_a500",
    description: "跟踪中证A500指数的ETF联接基金",
  },
];

// 真实指数数据
export const mockIndices: Index[] = [
  {
    id: "index_hs300",
    name: "沪深300",
    code: "000300",
    description: "沪深两市规模大、流动性好的300只股票",
  },
  {
    id: "index_zz500",
    name: "中证500",
    code: "000905",
    description: "中等市值股票的代表性指数",
  },
  {
    id: "index_zz1000",
    name: "中证1000",
    code: "000852",
    description: "中小市值股票的代表性指数",
  },
  {
    id: "index_zz2000",
    name: "中证2000",
    code: "932000",
    description: "小市值股票的代表性指数",
  },
  {
    id: "index_a100",
    name: "中证A100",
    code: "000903",
    description: "A股市场大盘股的代表性指数",
  },
  {
    id: "index_sz50",
    name: "上证50",
    code: "000016",
    description: "上海证券市场规模大、流动性好的50只股票",
  },
  {
    id: "index_kczz",
    name: "科创综指",
    code: "000680",
    description: "科创板市场的综合指数",
  },
  {
    id: "index_kcjg",
    name: "科创价格",
    code: "000681",
    description: "科创板市场的价格指数",
  },
  {
    id: "index_kc50",
    name: "科创50",
    code: "000688",
    description: "科创板市场规模大、流动性好的50只股票",
  },
  {
    id: "index_sz",
    name: "上证指数",
    code: "000001",
    description: "上海证券交易所综合股价指数",
  },
  // 为基金中引用但指数列表中没有的指数添加条目
  {
    id: "index_a500",
    name: "中证A500",
    code: "931643",
    description: "A股市场中等市值股票的代表性指数",
  },
  {
    id: "index_zz800",
    name: "中证800",
    code: "000906",
    description: "沪深300和中证500的合并指数",
  },
  {
    id: "index_cyb",
    name: "创业板指",
    code: "399006",
    description: "创业板市场的核心指数",
  },
  {
    id: "index_kccyb50",
    name: "科创创业50",
    code: "931643",
    description: "科创板和创业板的综合指数",
  },
];

// 生成模拟基金历史数据（用于演示和测试）
export function generateMockFundData(
  fundId: string,
  startDate: string,
  endDate: string
): FundData[] {
  const data: FundData[] = [];
  const start = new Date(startDate);
  const end = new Date(endDate);

  const currentDate = new Date(start);
  let baseValue = 1 + Math.random() * 2; // 基础净值 1.0-3.0

  // 根据基金类型设置不同的波动性
  const fund = mockFunds.find((f) => f.id === fundId);
  let volatility = 0.02; // 默认波动率
  let trend = 0.0001; // 默认趋势

  if (fund) {
    switch (fund.type) {
      case "index":
        volatility = 0.025;
        trend = 0.0002;
        break;
      case "hybrid":
        volatility = 0.02;
        trend = 0.0003;
        break;
      case "stock":
        volatility = 0.03;
        trend = 0.0001;
        break;
      case "bond":
        volatility = 0.005;
        trend = 0.0001;
        break;
    }
  }

  while (currentDate <= end) {
    // 跳过周末
    if (currentDate.getDay() !== 0 && currentDate.getDay() !== 6) {
      // 生成随机价格变动
      const randomChange = (Math.random() - 0.5) * volatility;
      const trendChange = trend;

      baseValue = baseValue * (1 + randomChange + trendChange);

      // 添加一些市场事件的影响
      if (Math.random() < 0.01) {
        // 1%概率的大幅波动
        const eventImpact = (Math.random() - 0.5) * 0.1;
        baseValue = baseValue * (1 + eventImpact);
      }

      // 确保净值不会变成负数
      baseValue = Math.max(baseValue, 0.1);

      data.push({
        date: currentDate.toISOString().split("T")[0],
        netAssetValue: Number(baseValue.toFixed(4)),
        accumulatedValue: Number((baseValue * 1.2).toFixed(4)), // 累计净值通常更高
        volume: Math.floor(Math.random() * 1000000) + 100000,
      });
    }

    currentDate.setDate(currentDate.getDate() + 1);
  }

  return data;
}

// 生成模拟指数历史数据（用于演示和测试）
export function generateMockIndexData(
  indexId: string,
  startDate: string,
  endDate: string
): IndexData[] {
  const data: IndexData[] = [];
  const start = new Date(startDate);
  const end = new Date(endDate);

  const currentDate = new Date(start);

  // 根据指数设置基础值
  let baseValue = 3000;
  let basePE = 15;
  let basePB = 1.5;

  const index = mockIndices.find((i) => i.id === indexId);
  if (index) {
    switch (index.code) {
      case "000300": // 沪深300
        baseValue = 3874;
        basePE = 12;
        basePB = 1.3;
        break;
      case "000905": // 中证500
        baseValue = 5762;
        basePE = 20;
        basePB = 2;
        break;
      case "000852": // 中证1000
        baseValue = 6153;
        basePE = 25;
        basePB = 2.2;
        break;
      case "932000": // 中证2000
        baseValue = 2601;
        basePE = 30;
        basePB = 2.5;
        break;
      case "000903": // 中证A100
        baseValue = 3698;
        basePE = 11;
        basePB = 1.2;
        break;
      case "000016": // 上证50
        baseValue = 2689;
        basePE = 10;
        basePB = 1.1;
        break;
      case "000680": // 科创综指
        baseValue = 1191;
        basePE = 40;
        basePB = 4;
        break;
      case "000681": // 科创价格
        baseValue = 1154;
        basePE = 38;
        basePB = 3.8;
        break;
      case "000688": // 科创50
        baseValue = 992;
        basePE = 45;
        basePB = 4.5;
        break;
      case "000001": // 上证指数
        baseValue = 3385;
        basePE = 14;
        basePB = 1.4;
        break;
      case "399006": // 创业板指
        baseValue = 2500;
        basePE = 35;
        basePB = 3;
        break;
      case "000906": // 中证800
        baseValue = 4500;
        basePE = 16;
        basePB = 1.6;
        break;
      case "931643": // 中证A500 或 科创创业50 (共用代码)
        baseValue = 3000;
        basePE = 25;
        basePB = 2.3;
        break;
    }
  }

  while (currentDate <= end) {
    // 跳过周末
    if (currentDate.getDay() !== 0 && currentDate.getDay() !== 6) {
      // 生成随机变动
      const randomChange = (Math.random() - 0.5) * 0.03;
      const trendChange = 0.0002;

      baseValue = baseValue * (1 + randomChange + trendChange);
      basePE = basePE * (1 + (Math.random() - 0.5) * 0.02);
      basePB = basePB * (1 + (Math.random() - 0.5) * 0.02);

      // 确保指标在合理范围内
      baseValue = Math.max(baseValue, 1000);
      basePE = Math.max(Math.min(basePE, 100), 5);
      basePB = Math.max(Math.min(basePB, 10), 0.5);

      data.push({
        date: currentDate.toISOString().split("T")[0],
        value: Number(baseValue.toFixed(2)),
        pe: Number(basePE.toFixed(2)),
        pb: Number(basePB.toFixed(2)),
        volume: Math.floor(Math.random() * 10000000) + 1000000,
      });
    }

    currentDate.setDate(currentDate.getDate() + 1);
  }

  return data;
}

// 获取基金数据的API（支持真实数据和模拟数据）
export async function getFundData(
  fundId: string,
  startDate: string,
  endDate: string
): Promise<FundData[]> {
  // 检查是否使用模拟数据
  if (APP_CONFIG.dataSource.useMockData) {
    // 模拟API延迟
    await new Promise((resolve) => setTimeout(resolve, 500));
    return generateMockFundData(fundId, startDate, endDate);
  }

  // 使用真实数据
  try {
    // 如果fundId是基金代码格式，直接使用
    let fundCode = fundId;

    // 如果是内部ID，需要转换为基金代码
    if (!fundId.match(/^\d{6}$/)) {
      const fund = mockFunds.find(f => f.id === fundId);
      if (fund) {
        fundCode = fund.code;
      } else {
        throw new Error(`未找到基金ID: ${fundId}`);
      }
    }

    const realData = await fetchFundData(fundCode, startDate, endDate);

    // 如果真实数据为空，回退到模拟数据
    if (realData.length === 0) {
      console.warn(`基金${fundCode}真实数据为空，使用模拟数据`);
      return generateMockFundData(fundId, startDate, endDate);
    }

    return realData;
  } catch (error) {
    console.error(`获取基金${fundId}真实数据失败，使用模拟数据:`, error);
    return generateMockFundData(fundId, startDate, endDate);
  }
}

// 获取指数数据的API（支持真实数据和模拟数据）
export async function getIndexData(
  indexId: string,
  startDate: string,
  endDate: string
): Promise<IndexData[]> {
  // 检查是否使用模拟数据
  if (APP_CONFIG.dataSource.useMockData) {
    // 模拟API延迟
    await new Promise((resolve) => setTimeout(resolve, 300));
    return generateMockIndexData(indexId, startDate, endDate);
  }

  // 使用真实数据
  try {
    // 如果indexId是指数代码格式，直接使用
    let indexCode = indexId;

    // 如果是内部ID，需要转换为指数代码
    if (!indexId.match(/^\d{6}$/)) {
      const index = mockIndices.find(i => i.id === indexId);
      if (index) {
        indexCode = index.code;
      } else {
        throw new Error(`未找到指数ID: ${indexId}`);
      }
    }

    const { fetchIndexData } = await import('@/lib/api/indexApi');
    const realData = await fetchIndexData(indexCode, startDate, endDate);

    // 如果真实数据为空，回退到模拟数据
    if (realData.length === 0) {
      console.warn(`指数${indexCode}真实数据为空，使用模拟数据`);
      return generateMockIndexData(indexId, startDate, endDate);
    }

    return realData;
  } catch (error) {
    console.error(`获取指数${indexId}真实数据失败，使用模拟数据:`, error);
    return generateMockIndexData(indexId, startDate, endDate);
  }
}

// 获取所有基金列表
export async function getFunds(): Promise<Fund[]> {
  await new Promise((resolve) => setTimeout(resolve, 200));
  return mockFunds;
}

// 获取所有指数列表
export async function getIndices(): Promise<Index[]> {
  await new Promise((resolve) => setTimeout(resolve, 200));
  return mockIndices;
}

// 根据基金ID获取基金信息
export function getFundById(fundId: string): Fund | undefined {
  return mockFunds.find((fund) => fund.id === fundId);
}

// 根据指数ID获取指数信息
export function getIndexById(indexId: string): Index | undefined {
  return mockIndices.find((index) => index.id === indexId);
}

// 搜索基金（支持代码和名称）
export async function searchFunds(query: string): Promise<Fund[]> {
  if (!query.trim()) {
    return mockFunds;
  }

  const queryLower = query.toLowerCase();

  // 先在本地基金列表中搜索
  const localResults = mockFunds.filter(fund =>
    fund.code.includes(query) ||
    fund.name.toLowerCase().includes(queryLower)
  );

  // 如果不使用模拟数据且查询看起来像基金代码，尝试验证真实基金
  if (!APP_CONFIG.dataSource.useMockData && query.match(/^\d{6}$/)) {
    try {
      const fundInfo = await getFundBasicInfo(query);
      if (fundInfo && !localResults.some(f => f.code === query)) {
        // 添加新发现的基金到结果中
        localResults.push({
          id: `fund_${query}`,
          name: fundInfo.name,
          code: fundInfo.code,
          type: 'hybrid', // 默认类型，实际应从API获取
          description: `通过代码${query}搜索到的基金`
        });
      }
    } catch (error) {
      console.warn(`验证基金代码${query}失败:`, error);
    }
  }

  return localResults;
}

// 验证基金代码是否有效
export async function validateFund(fundCode: string): Promise<boolean> {
  // 先检查本地列表
  const localFund = mockFunds.find(f => f.code === fundCode);
  if (localFund) {
    return true;
  }

  // 如果不使用模拟数据，检查真实API
  if (!APP_CONFIG.dataSource.useMockData) {
    try {
      const { validateFund } = await import('@/lib/api/fundApi');
      return await validateFund(fundCode);
    } catch (error) {
      console.error(`验证基金代码${fundCode}失败:`, error);
      return false;
    }
  }

  return false;
}

// 添加新基金到列表（用于动态发现的基金）
export function addFundToList(fund: Fund): void {
  const existingIndex = mockFunds.findIndex(f => f.code === fund.code);
  if (existingIndex >= 0) {
    // 更新现有基金信息
    mockFunds[existingIndex] = { ...mockFunds[existingIndex], ...fund };
  } else {
    // 添加新基金
    mockFunds.push(fund);
  }
}
